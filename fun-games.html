<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 Fun Games 🎮</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #333;
        }

        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        h1 {
            color: #ff6b6b;
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .score-board {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 15px;
        }

        .score-item {
            text-align: center;
        }

        .score-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }

        .score-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #4ecdc4;
        }

        .difficulty-selector {
            margin-bottom: 20px;
        }

        .difficulty-btn {
            background: #45b7d1;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .difficulty-btn:hover {
            background: #3498db;
            transform: translateY(-2px);
        }

        .difficulty-btn.active {
            background: #e74c3c;
            transform: scale(1.1);
        }

        .problem-area {
            background: #fff3cd;
            border: 3px solid #ffc107;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            font-size: 2em;
            font-weight: bold;
            color: #856404;
        }

        .answer-input {
            font-size: 1.5em;
            padding: 15px;
            border: 3px solid #28a745;
            border-radius: 10px;
            text-align: center;
            width: 150px;
            margin: 20px 10px;
        }

        .submit-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .submit-btn:hover {
            background: #218838;
            transform: scale(1.05);
        }

        .feedback {
            font-size: 1.3em;
            font-weight: bold;
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .correct {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }

        .incorrect {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }

        .new-game-btn {
            background: #6f42c1;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            border-radius: 25px;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s ease;
        }

        .new-game-btn:hover {
            background: #5a32a3;
            transform: scale(1.05);
        }

        .celebration {
            animation: bounce 0.6s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-20px); }
            80% { transform: translateY(-10px); }
        }

        .game-selection {
            display: block;
        }

        .game-play {
            display: none;
        }

        .game-selector {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .game-option {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 30px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 1.3em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .game-option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        }

        .game-option.addition {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .game-option.subtraction {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .game-option.multiplication {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }

        .game-option.division {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }

        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            margin-bottom: 20px;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #5a6268;
            transform: scale(1.05);
        }

        .trick-box {
            background: #e7f3ff;
            border: 2px solid #0066cc;
            border-radius: 15px;
            padding: 15px;
            margin: 15px 0;
            font-size: 0.9em;
            color: #0066cc;
            display: none;
        }

        .trick-box.show {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .trick-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #004499;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- Game Selection Screen -->
        <div class="game-selection" id="gameSelection">
            <h1>🎮 Fun Games 🎮</h1>
            <p style="font-size: 1.2em; margin-bottom: 20px; color: #666;">Choose your favorite math game!</p>
            
            <div class="game-selector">
                <button class="game-option addition" onclick="selectGame('addition')">
                    ➕<br>Addition<br><small>Add numbers together</small>
                </button>
                <button class="game-option subtraction" onclick="selectGame('subtraction')">
                    ➖<br>Subtraction<br><small>Take away numbers</small>
                </button>
                <button class="game-option multiplication" onclick="selectGame('multiplication')">
                    ✖️<br>Multiplication<br><small>Times tables</small>
                </button>
                <button class="game-option division" onclick="selectGame('division')">
                    ➗<br>Division<br><small>Share equally</small>
                </button>
            </div>
        </div>

        <!-- Game Play Screen -->
        <div class="game-play" id="gamePlay">
            <button class="back-btn" onclick="backToSelection()">← Back to Games</button>
            <h1 id="gameTitle">🎯 Math Game 🎯</h1>
            
            <div class="score-board">
                <div class="score-item">
                    <div class="score-label">Score</div>
                    <div class="score-value" id="score">0</div>
                </div>
                <div class="score-item">
                    <div class="score-label">Correct</div>
                    <div class="score-value" id="correct">0</div>
                </div>
                <div class="score-item">
                    <div class="score-label">Total</div>
                    <div class="score-value" id="total">0</div>
                </div>
            </div>

            <div class="difficulty-selector">
                <button class="difficulty-btn active" onclick="setDifficulty('easy')">Easy (1-10)</button>
                <button class="difficulty-btn" onclick="setDifficulty('medium')">Medium (1-50)</button>
                <button class="difficulty-btn" onclick="setDifficulty('hard')">Hard (1-100)</button>
            </div>

            <div class="trick-box" id="trickBox">
                <div class="trick-title">💡 Helpful Trick:</div>
                <div id="trickContent"></div>
            </div>

            <div class="problem-area" id="problem">
                Click "New Problem" to start!
            </div>

            <div>
                <input type="number" class="answer-input" id="answer" placeholder="?" onkeypress="handleKeyPress(event)">
                <br>
                <button class="submit-btn" onclick="checkAnswer()">Check Answer</button>
                <button class="submit-btn" onclick="newProblem()">New Problem</button>
            </div>

            <div class="feedback" id="feedback"></div>

            <button class="new-game-btn" onclick="resetGame()">🎮 New Game</button>
        </div>
    </div>

    <script>
        let currentProblem = {};
        let score = 0;
        let correct = 0;
        let total = 0;
        let difficulty = 'easy';
        let currentGame = 'addition';

        const difficulties = {
            easy: { min: 1, max: 10 },
            medium: { min: 1, max: 50 },
            hard: { min: 1, max: 100 }
        };

        const gameInfo = {
            addition: {
                title: "➕ Addition Game ➕",
                symbol: "+",
                operation: (a, b) => a + b
            },
            subtraction: {
                title: "➖ Subtraction Game ➖",
                symbol: "−",
                operation: (a, b) => a - b
            },
            multiplication: {
                title: "✖️ Multiplication Game ✖️",
                symbol: "×",
                operation: (a, b) => a * b
            },
            division: {
                title: "➗ Division Game ➗",
                symbol: "÷",
                operation: (a, b) => a / b
            }
        };

        const tricks = {
            addition: {
                medium: "💡 Tip: Break big numbers into smaller parts! For 27 + 15, think: 27 + 10 = 37, then 37 + 5 = 42",
                hard: "💡 Tip: Round to nearest 10! For 67 + 28, think: 67 + 30 = 97, then subtract 2 = 95"
            },
            subtraction: {
                medium: "💡 Tip: Add up to subtract! For 42 - 17, think: 17 + ? = 42. Count up: 17 + 3 = 20, 20 + 22 = 42, so 3 + 22 = 25",
                hard: "💡 Tip: Use friendly numbers! For 83 - 29, think: 83 - 30 = 53, then add back 1 = 54"
            },
            multiplication: {
                medium: "💡 Tip: Use doubles! For 6 × 8, think: 6 × 4 = 24, so 6 × 8 = 24 × 2 = 48",
                hard: "💡 Tip: Break it down! For 23 × 4, think: (20 × 4) + (3 × 4) = 80 + 12 = 92"
            },
            division: {
                medium: "💡 Tip: Think multiplication! For 48 ÷ 6, ask: 6 × ? = 48. You know 6 × 8 = 48!",
                hard: "💡 Tip: Use factors! For 84 ÷ 12, think: 84 ÷ 4 = 21, then 21 ÷ 3 = 7 (since 12 = 4 × 3)"
            }
        };

        const encouragingMessages = [
            "🌟 Awesome job!",
            "🎉 You're amazing!",
            "⭐ Fantastic!",
            "🏆 Super star!",
            "🎊 Brilliant!",
            "🌈 Outstanding!",
            "🎯 Perfect!",
            "🚀 You rock!"
        ];

        const tryAgainMessages = [
            "🤔 Try again!",
            "💪 You can do it!",
            "🎯 Almost there!",
            "🌟 Keep trying!",
            "💡 Think again!",
            "🎈 Don't give up!"
        ];

        function selectGame(game) {
            currentGame = game;
            document.getElementById('gameSelection').style.display = 'none';
            document.getElementById('gamePlay').style.display = 'block';
            document.getElementById('gameTitle').textContent = gameInfo[game].title;
            resetGame();
            newProblem();
        }

        function backToSelection() {
            document.getElementById('gameSelection').style.display = 'block';
            document.getElementById('gamePlay').style.display = 'none';
            resetGame();
        }

        function setDifficulty(level) {
            difficulty = level;
            document.querySelectorAll('.difficulty-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            showTrick();
            newProblem();
        }

        function showTrick() {
            const trickBox = document.getElementById('trickBox');
            const trickContent = document.getElementById('trickContent');

            if (difficulty !== 'easy' && tricks[currentGame] && tricks[currentGame][difficulty]) {
                trickContent.textContent = tricks[currentGame][difficulty];
                trickBox.classList.add('show');
            } else {
                trickBox.classList.remove('show');
            }
        }

        function generateProblem() {
            const range = difficulties[difficulty];
            let num1 = Math.floor(Math.random() * range.max) + range.min;
            let num2 = Math.floor(Math.random() * range.max) + range.min;

            // Special handling for different operations
            if (currentGame === 'subtraction') {
                // Ensure num1 >= num2 for positive results
                if (num1 < num2) [num1, num2] = [num2, num1];
            } else if (currentGame === 'division') {
                // For division, ensure clean division (no remainders)
                const divisors = difficulty === 'easy' ? [2, 3, 4, 5] :
                               difficulty === 'medium' ? [2, 3, 4, 5, 6, 7, 8, 9, 10] :
                               [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
                num2 = divisors[Math.floor(Math.random() * divisors.length)];
                const quotient = Math.floor(Math.random() * (range.max / num2)) + 1;
                num1 = num2 * quotient;
            }

            const answer = gameInfo[currentGame].operation(num1, num2);

            currentProblem = {
                num1: num1,
                num2: num2,
                answer: Math.round(answer * 100) / 100 // Round to 2 decimal places
            };

            const symbol = gameInfo[currentGame].symbol;
            document.getElementById('problem').textContent = `${num1} ${symbol} ${num2} = ?`;
            document.getElementById('answer').value = '';
            document.getElementById('answer').focus();
            document.getElementById('feedback').textContent = '';
            document.getElementById('feedback').className = 'feedback';
        }

        function newProblem() {
            generateProblem();
        }

        function checkAnswer() {
            const userAnswer = parseFloat(document.getElementById('answer').value);
            const feedbackElement = document.getElementById('feedback');

            if (isNaN(userAnswer)) {
                feedbackElement.textContent = "Please enter a number! 🔢";
                feedbackElement.className = 'feedback incorrect';
                return;
            }

            total++;

            if (Math.abs(userAnswer - currentProblem.answer) < 0.01) { // Allow small floating point differences
                correct++;
                score += 10;
                const message = encouragingMessages[Math.floor(Math.random() * encouragingMessages.length)];
                feedbackElement.textContent = message;
                feedbackElement.className = 'feedback correct celebration';

                setTimeout(() => {
                    newProblem();
                }, 1500);
            } else {
                const message = tryAgainMessages[Math.floor(Math.random() * tryAgainMessages.length)];
                feedbackElement.textContent = `${message} The answer is ${currentProblem.answer}`;
                feedbackElement.className = 'feedback incorrect';

                setTimeout(() => {
                    newProblem();
                }, 2500);
            }

            updateScoreBoard();
        }

        function updateScoreBoard() {
            document.getElementById('score').textContent = score;
            document.getElementById('correct').textContent = correct;
            document.getElementById('total').textContent = total;
        }

        function resetGame() {
            score = 0;
            correct = 0;
            total = 0;
            difficulty = 'easy';
            updateScoreBoard();

            // Reset difficulty buttons
            document.querySelectorAll('.difficulty-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelector('.difficulty-btn').classList.add('active');

            document.getElementById('problem').textContent = "Click 'New Problem' to start!";
            document.getElementById('answer').value = '';
            document.getElementById('feedback').textContent = '';
            document.getElementById('feedback').className = 'feedback';

            // Hide trick box
            document.getElementById('trickBox').classList.remove('show');
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                checkAnswer();
            }
        }

        // Initialize the game
        window.onload = function() {
            // Start with game selection screen
            document.getElementById('gameSelection').style.display = 'block';
            document.getElementById('gamePlay').style.display = 'none';
        };
    </script>
</body>
</html>
