<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Birthday Input</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        h1 {
            color: #333;
            font-size: 2.5em;
            margin: 0;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1em;
            padding: 8px 16px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .nav-links a:hover {
            background-color: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        @media (max-width: 600px) {
            .header {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            h1 {
                font-size: 2em;
            }
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: bold;
            font-size: 1.1em;
        }

        input[type="text"],
        input[type="date"],
        input[type="email"] {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        input[type="text"]:focus,
        input[type="date"]:focus,
        input[type="email"]:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .result {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            display: none;
        }

        .result h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .result p {
            color: #666;
            line-height: 1.6;
        }

        .age-display {
            font-size: 1.2em;
            font-weight: bold;
            color: #667eea;
        }

        .about-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .about-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .about-section p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .about-section ul {
            color: #666;
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .about-section li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .main-menu {
            text-align: center;
            margin-bottom: 30px;
        }

        .main-menu h2 {
            color: #333;
            margin-bottom: 25px;
            font-size: 1.8em;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .menu-btn {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .menu-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
        }

        .states-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .state-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .state-item:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .state-item h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .state-item p {
            color: #666;
            margin: 0;
            font-size: 0.9em;
        }

        .oceans-list {
            margin: 20px 0;
        }

        .ocean-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            margin-bottom: 15px;
        }

        .ocean-item h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .ocean-item p {
            color: #666;
            margin: 0;
            line-height: 1.6;
        }

        .capital-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            margin-top: 10px;
            transition: background 0.3s ease;
        }

        .capital-btn:hover {
            background: #5a6fd8;
        }

        .game-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 20px 0;
        }
        
        .game-info {
            text-align: center;
            margin-bottom: 15px;
            width: 100%;
        }
        
        @media (max-width: 500px) {
            #gameCanvas {
                width: 100%;
                height: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <nav class="nav-links">
                <a href="#about" id="aboutLink">About Us</a>
            </nav>
            <h1>� Multi-Purpose Portal</h1>
        </div>

        <div id="mainMenu" class="main-menu">
            <h2>Choose an Option</h2>
            <div class="menu-grid">
                <button class="menu-btn" id="birthdayBtn">�🎂 Birthday Calculator</button>
                <button class="menu-btn" id="statesBtn">🏛️ Total States Under India</button>
                <button class="menu-btn" id="oceansBtn">🌊 Total Oceans in the World</button>
                <button class="menu-btn" id="aboutBtn">👤 About Us</button>
                <button class="menu-btn" id="pathfinderBtn">🎮 Kids Path Finder Game</button>
            </div>
        </div>

        <!-- Birthday Calculator Section -->
        <div id="birthdaySection" class="section">
            <h2>🎂 Birthday Calculator</h2>
            <form id="birthdayForm">
                <div class="form-group">
                    <label for="name">Full Name:</label>
                    <input type="text" id="name" name="name" required placeholder="Enter your full name">
                </div>

                <div class="form-group">
                    <label for="email">Email Address:</label>
                    <input type="email" id="email" name="email" required placeholder="Enter your email">
                </div>

                <div class="form-group">
                    <label for="birthday">Date of Birth:</label>
                    <input type="date" id="birthday" name="birthday" required>
                </div>

                <button type="submit" class="btn">Calculate Age & Save</button>
            </form>

            <div id="result" class="result">
                <h3>Birthday Information</h3>
                <p><strong>Name:</strong> <span id="displayName"></span></p>
                <p><strong>Email:</strong> <span id="displayEmail"></span></p>
                <p><strong>Date of Birth:</strong> <span id="displayBirthday"></span></p>
                <p><strong>Current Age:</strong> <span id="displayAge" class="age-display"></span></p>
                <p><strong>Days until next birthday:</strong> <span id="daysUntilBirthday" class="age-display"></span></p>
            </div>
            <button type="button" class="btn" id="backToMenuFromBirthday" style="margin-top: 20px;">Back to Menu</button>
        </div>

        <!-- States Section -->
        <div id="statesSection" class="section">
            <h2>🏛️ Total States Under India</h2>
            <p><strong>Total States: 28 | Union Territories: 8</strong></p>
            <div id="statesList" class="states-grid">
                <!-- States will be populated by JavaScript -->
            </div>
            <button type="button" class="btn" id="backToMenuFromStates" style="margin-top: 20px;">Back to Menu</button>
        </div>

        <!-- State Details Section -->
        <div id="stateDetailsSection" class="section">
            <div id="stateDetails">
                <!-- State details will be populated by JavaScript -->
            </div>
            <button type="button" class="btn" id="backToStatesFromDetails" style="margin-top: 20px;">Back to States</button>
        </div>

        <!-- Oceans Section -->
        <div id="oceansSection" class="section">
            <h2>🌊 Total Oceans in the World</h2>
            <p><strong>Total Oceans: 5</strong></p>
            <div class="oceans-list">
                <div class="ocean-item">
                    <h3>1. Pacific Ocean</h3>
                    <p>The largest and deepest ocean, covering about 46% of the world's ocean surface.</p>
                </div>
                <div class="ocean-item">
                    <h3>2. Atlantic Ocean</h3>
                    <p>The second-largest ocean, known for its distinctive S-shape.</p>
                </div>
                <div class="ocean-item">
                    <h3>3. Indian Ocean</h3>
                    <p>The third-largest ocean, bounded by Africa, Asia, Australia, and Antarctica.</p>
                </div>
                <div class="ocean-item">
                    <h3>4. Southern (Antarctic) Ocean</h3>
                    <p>Surrounds Antarctica and is the newest named ocean.</p>
                </div>
                <div class="ocean-item">
                    <h3>5. Arctic Ocean</h3>
                    <p>The smallest and shallowest ocean, located around the North Pole.</p>
                </div>
            </div>
            <button type="button" class="btn" id="backToMenuFromOceans" style="margin-top: 20px;">Back to Menu</button>
        </div>

        <!-- About Us Section -->
        <div id="aboutSection" class="section">
            <h3>About Us</h3>
            <p>My name is <strong>"PRASHANT DESHBHARTAR"</strong>.</p>
            <p>I have come to know that there is an opening for System Admin in your company.</p>
            <p>After reading all requirements, I think I am a suitable candidate for your organization.</p>
            <p>It's my pleasure to present myself in your company.</p>
            <p>A Detailed resume is attached with this mail.</p>
            <p>Kindly go through it.</p>
            <button type="button" class="btn" id="backToMenuFromAbout" style="margin-top: 20px;">Back to Menu</button>
        </div>
    </div>

    <script>
        // States data with capitals and travel information
        const statesData = {
            "Andhra Pradesh": {
                capital: "Amaravati",
                places: ["Kanaka Durga Temple", "Prakasam Barrage"],
                trainRoute: "Take Ahmedabad-Vijayawada Express (12804) to Vijayawada, then local transport to Amaravati"
            },
            "Arunachal Pradesh": {
                capital: "Itanagar",
                places: ["Ita Fort", "Ganga Lake"],
                trainRoute: "Take train to Guwahati, then bus/taxi to Itanagar (No direct train)"
            },
            "Assam": {
                capital: "Dispur",
                places: ["Kamakhya Temple", "Assam State Museum"],
                trainRoute: "Take Kamrup Express (15959) from Ahmedabad to Guwahati, then local transport to Dispur"
            },
            "Bihar": {
                capital: "Patna",
                places: ["Golghar", "Patna Museum"],
                trainRoute: "Take Sabarmati Express (19168) from Ahmedabad to Patna Junction"
            },
            "Chhattisgarh": {
                capital: "Raipur",
                places: ["Mahant Ghasidas Memorial Museum", "Vivekananda Sarovar"],
                trainRoute: "Take Ahmedabad-Raipur Express (18422) from Ahmedabad to Raipur"
            },
            "Goa": {
                capital: "Panaji",
                places: ["Basilica of Bom Jesus", "Dona Paula Beach"],
                trainRoute: "Take Ahmedabad-Vasco Express (19216) to Vasco, then bus to Panaji"
            },
            "Gujarat": {
                capital: "Gandhinagar",
                places: ["Akshardham Temple", "Sarita Udyan"],
                trainRoute: "Take local train or bus from Ahmedabad to Gandhinagar (30 km)"
            },
            "Haryana": {
                capital: "Chandigarh",
                places: ["Rock Garden", "Sukhna Lake"],
                trainRoute: "Take Ashram Express (12916) from Ahmedabad to Chandigarh"
            },
            "Himachal Pradesh": {
                capital: "Shimla",
                places: ["Mall Road", "Jakhu Temple"],
                trainRoute: "Take train to Chandigarh, then toy train or bus to Shimla"
            },
            "Jharkhand": {
                capital: "Ranchi",
                places: ["Jagannath Temple", "Rock Garden"],
                trainRoute: "Take train to Hatia (Ranchi) via Delhi or Kolkata"
            },
            "Karnataka": {
                capital: "Bengaluru",
                places: ["Lalbagh Botanical Garden", "Bangalore Palace"],
                trainRoute: "Take Ahmedabad-Bangalore Express (16587) from Ahmedabad to Bangalore"
            },
            "Kerala": {
                capital: "Thiruvananthapuram",
                places: ["Padmanabhaswamy Temple", "Kovalam Beach"],
                trainRoute: "Take Sabari Express (16526) from Ahmedabad to Thiruvananthapuram"
            },
            "Madhya Pradesh": {
                capital: "Bhopal",
                places: ["Taj-ul-Masajid", "Upper Lake"],
                trainRoute: "Take Shanti Express (12956) from Ahmedabad to Bhopal"
            },
            "Maharashtra": {
                capital: "Mumbai",
                places: ["Gateway of India", "Marine Drive"],
                trainRoute: "Take Karnavati Express (12834) from Ahmedabad to Mumbai Central"
            },
            "Manipur": {
                capital: "Imphal",
                places: ["Kangla Fort", "Loktak Lake"],
                trainRoute: "Take train to Guwahati, then bus to Imphal (No direct train)"
            },
            "Meghalaya": {
                capital: "Shillong",
                places: ["Elephant Falls", "Shillong Peak"],
                trainRoute: "Take train to Guwahati, then bus to Shillong (No direct train)"
            },
            "Mizoram": {
                capital: "Aizawl",
                places: ["Mizoram State Museum", "Durtlang Hills"],
                trainRoute: "Take train to Silchar, then bus to Aizawl (No direct train)"
            },
            "Nagaland": {
                capital: "Kohima",
                places: ["Kohima War Cemetery", "Dzukou Valley"],
                trainRoute: "Take train to Dimapur, then bus to Kohima"
            },
            "Odisha": {
                capital: "Bhubaneswar",
                places: ["Lingaraj Temple", "Udayagiri Caves"],
                trainRoute: "Take Ahmedabad-Puri Express (18422) to Bhubaneswar"
            },
            "Punjab": {
                capital: "Chandigarh",
                places: ["Rock Garden", "Sukhna Lake"],
                trainRoute: "Take Ashram Express (12916) from Ahmedabad to Chandigarh"
            },
            "Rajasthan": {
                capital: "Jaipur",
                places: ["Hawa Mahal", "City Palace"],
                trainRoute: "Take Ashram Express (12916) from Ahmedabad to Jaipur"
            },
            "Sikkim": {
                capital: "Gangtok",
                places: ["Rumtek Monastery", "MG Marg"],
                trainRoute: "Take train to New Jalpaiguri, then bus/taxi to Gangtok"
            },
            "Tamil Nadu": {
                capital: "Chennai",
                places: ["Marina Beach", "Kapaleeshwarar Temple"],
                trainRoute: "Take Chennai Express (12834) from Ahmedabad to Chennai Central"
            },
            "Telangana": {
                capital: "Hyderabad",
                places: ["Charminar", "Golconda Fort"],
                trainRoute: "Take Karnavati Express (12834) from Ahmedabad to Hyderabad"
            },
            "Tripura": {
                capital: "Agartala",
                places: ["Ujjayanta Palace", "Neermahal"],
                trainRoute: "Take train to Agartala via Guwahati"
            },
            "Uttar Pradesh": {
                capital: "Lucknow",
                places: ["Bara Imambara", "Rumi Darwaza"],
                trainRoute: "Take Sabarmati Express (19168) from Ahmedabad to Lucknow"
            },
            "Uttarakhand": {
                capital: "Dehradun",
                places: ["Robber's Cave", "Sahastradhara"],
                trainRoute: "Take Dehradun Express (19020) from Ahmedabad to Dehradun"
            },
            "West Bengal": {
                capital: "Kolkata",
                places: ["Victoria Memorial", "Howrah Bridge"],
                trainRoute: "Take Sabarmati Express (19168) from Ahmedabad to Howrah"
            }
        };

        // Navigation functions
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.style.display = 'none';
            });
            document.getElementById('mainMenu').style.display = 'none';

            // Show selected section
            document.getElementById(sectionId).style.display = 'block';
        }

        function showMainMenu() {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.style.display = 'none';
            });
            document.getElementById('mainMenu').style.display = 'block';
        }

        // Menu button event listeners
        document.getElementById('birthdayBtn').addEventListener('click', () => showSection('birthdaySection'));
        document.getElementById('statesBtn').addEventListener('click', () => {
            showSection('statesSection');
            loadStates();
        });
        document.getElementById('oceansBtn').addEventListener('click', () => showSection('oceansSection'));
        document.getElementById('aboutBtn').addEventListener('click', () => showSection('aboutSection'));

        // Back to menu buttons
        document.getElementById('backToMenuFromBirthday').addEventListener('click', showMainMenu);
        document.getElementById('backToMenuFromStates').addEventListener('click', showMainMenu);
        document.getElementById('backToMenuFromOceans').addEventListener('click', showMainMenu);
        document.getElementById('backToMenuFromAbout').addEventListener('click', showMainMenu);
        document.getElementById('backToStatesFromDetails').addEventListener('click', () => {
            showSection('statesSection');
            loadStates();
        });

        // About Us link in header
        document.getElementById('aboutLink').addEventListener('click', function(e) {
            e.preventDefault();
            showSection('aboutSection');
        });

        // Load states function
        function loadStates() {
            const statesList = document.getElementById('statesList');
            statesList.innerHTML = '';

            Object.keys(statesData).forEach(state => {
                const stateItem = document.createElement('div');
                stateItem.className = 'state-item';
                stateItem.innerHTML = `
                    <h4>${state}</h4>
                    <p>Capital: ${statesData[state].capital}</p>
                    <button class="capital-btn" onclick="showStateDetails('${state}')">View Capital Details</button>
                `;
                statesList.appendChild(stateItem);
            });
        }

        // Show state details function
        function showStateDetails(stateName) {
            const stateData = statesData[stateName];
            const stateDetails = document.getElementById('stateDetails');

            stateDetails.innerHTML = `
                <h2>${stateName} - ${stateData.capital}</h2>
                <div class="about-section">
                    <h3>Best Places to Visit in ${stateData.capital}</h3>
                    <ul>
                        <li><strong>${stateData.places[0]}</strong></li>
                        <li><strong>${stateData.places[1]}</strong></li>
                    </ul>
                    <h3>How to Reach from Ahmedabad by Train</h3>
                    <p>${stateData.trainRoute}</p>
                </div>
            `;

            showSection('stateDetailsSection');
        }

        // Make showStateDetails global
        window.showStateDetails = showStateDetails;

        // Birthday calculator functionality
        document.getElementById('birthdayForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const birthday = document.getElementById('birthday').value;

            if (name && email && birthday) {
                const birthDate = new Date(birthday);
                const today = new Date();

                // Calculate age
                let age = today.getFullYear() - birthDate.getFullYear();
                const monthDiff = today.getMonth() - birthDate.getMonth();

                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                    age--;
                }

                // Calculate days until next birthday
                const nextBirthday = new Date(today.getFullYear(), birthDate.getMonth(), birthDate.getDate());
                if (nextBirthday < today) {
                    nextBirthday.setFullYear(today.getFullYear() + 1);
                }

                const daysUntilBirthday = Math.ceil((nextBirthday - today) / (1000 * 60 * 60 * 24));

                // Display results
                document.getElementById('displayName').textContent = name;
                document.getElementById('displayEmail').textContent = email;
                document.getElementById('displayBirthday').textContent = new Date(birthday).toLocaleDateString();
                document.getElementById('displayAge').textContent = age + ' years old';
                document.getElementById('daysUntilBirthday').textContent = daysUntilBirthday + ' days';

                document.getElementById('result').style.display = 'block';

                // Save to localStorage
                const birthdayData = {
                    name: name,
                    email: email,
                    birthday: birthday,
                    age: age,
                    daysUntilBirthday: daysUntilBirthday,
                    savedAt: new Date().toISOString()
                };

                localStorage.setItem('birthdayData', JSON.stringify(birthdayData));
                console.log('Birthday data saved to localStorage:', birthdayData);
            }
        });

        // Load saved data on page load
        window.addEventListener('load', function() {
            const savedData = localStorage.getItem('birthdayData');
            if (savedData) {
                const data = JSON.parse(savedData);
                document.getElementById('name').value = data.name;
                document.getElementById('email').value = data.email;
                document.getElementById('birthday').value = data.birthday;
            }
        });
    </script>
</body>
</html>


