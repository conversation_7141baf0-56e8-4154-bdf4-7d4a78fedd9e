<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Kids Addition Game 🎯</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #333;
        }

        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        h1 {
            color: #ff6b6b;
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .score-board {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 15px;
        }

        .score-item {
            text-align: center;
        }

        .score-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }

        .score-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #4ecdc4;
        }

        .difficulty-selector {
            margin-bottom: 20px;
        }

        .difficulty-btn {
            background: #45b7d1;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .difficulty-btn:hover {
            background: #3498db;
            transform: translateY(-2px);
        }

        .difficulty-btn.active {
            background: #e74c3c;
            transform: scale(1.1);
        }

        .problem-area {
            background: #fff3cd;
            border: 3px solid #ffc107;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            font-size: 2em;
            font-weight: bold;
            color: #856404;
        }

        .answer-input {
            font-size: 1.5em;
            padding: 15px;
            border: 3px solid #28a745;
            border-radius: 10px;
            text-align: center;
            width: 150px;
            margin: 20px 10px;
        }

        .submit-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .submit-btn:hover {
            background: #218838;
            transform: scale(1.05);
        }

        .feedback {
            font-size: 1.3em;
            font-weight: bold;
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .correct {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }

        .incorrect {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }

        .new-game-btn {
            background: #6f42c1;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            border-radius: 25px;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s ease;
        }

        .new-game-btn:hover {
            background: #5a32a3;
            transform: scale(1.05);
        }

        .celebration {
            animation: bounce 0.6s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-20px); }
            80% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🎯 Addition Game 🎯</h1>
        
        <div class="score-board">
            <div class="score-item">
                <div class="score-label">Score</div>
                <div class="score-value" id="score">0</div>
            </div>
            <div class="score-item">
                <div class="score-label">Correct</div>
                <div class="score-value" id="correct">0</div>
            </div>
            <div class="score-item">
                <div class="score-label">Total</div>
                <div class="score-value" id="total">0</div>
            </div>
        </div>

        <div class="difficulty-selector">
            <button class="difficulty-btn active" onclick="setDifficulty('easy')">Easy (1-10)</button>
            <button class="difficulty-btn" onclick="setDifficulty('medium')">Medium (1-50)</button>
            <button class="difficulty-btn" onclick="setDifficulty('hard')">Hard (1-100)</button>
        </div>

        <div class="problem-area" id="problem">
            Click "New Problem" to start!
        </div>

        <div>
            <input type="number" class="answer-input" id="answer" placeholder="?" onkeypress="handleKeyPress(event)">
            <br>
            <button class="submit-btn" onclick="checkAnswer()">Check Answer</button>
            <button class="submit-btn" onclick="newProblem()">New Problem</button>
        </div>

        <div class="feedback" id="feedback"></div>

        <button class="new-game-btn" onclick="resetGame()">🎮 New Game</button>
    </div>

    <script>
        let currentProblem = {};
        let score = 0;
        let correct = 0;
        let total = 0;
        let difficulty = 'easy';

        const difficulties = {
            easy: { min: 1, max: 10 },
            medium: { min: 1, max: 50 },
            hard: { min: 1, max: 100 }
        };

        const encouragingMessages = [
            "🌟 Awesome job!",
            "🎉 You're amazing!",
            "⭐ Fantastic!",
            "🏆 Super star!",
            "🎊 Brilliant!",
            "🌈 Outstanding!",
            "🎯 Perfect!",
            "🚀 You rock!"
        ];

        const tryAgainMessages = [
            "🤔 Try again!",
            "💪 You can do it!",
            "🎯 Almost there!",
            "🌟 Keep trying!",
            "💡 Think again!",
            "🎈 Don't give up!"
        ];

        function setDifficulty(level) {
            difficulty = level;
            document.querySelectorAll('.difficulty-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            newProblem();
        }

        function generateProblem() {
            const range = difficulties[difficulty];
            const num1 = Math.floor(Math.random() * range.max) + range.min;
            const num2 = Math.floor(Math.random() * range.max) + range.min;
            
            currentProblem = {
                num1: num1,
                num2: num2,
                answer: num1 + num2
            };
            
            document.getElementById('problem').textContent = `${num1} + ${num2} = ?`;
            document.getElementById('answer').value = '';
            document.getElementById('answer').focus();
            document.getElementById('feedback').textContent = '';
            document.getElementById('feedback').className = 'feedback';
        }

        function newProblem() {
            generateProblem();
        }

        function checkAnswer() {
            const userAnswer = parseInt(document.getElementById('answer').value);
            const feedbackElement = document.getElementById('feedback');
            
            if (isNaN(userAnswer)) {
                feedbackElement.textContent = "Please enter a number! 🔢";
                feedbackElement.className = 'feedback incorrect';
                return;
            }
            
            total++;
            
            if (userAnswer === currentProblem.answer) {
                correct++;
                score += 10;
                const message = encouragingMessages[Math.floor(Math.random() * encouragingMessages.length)];
                feedbackElement.textContent = message;
                feedbackElement.className = 'feedback correct celebration';
                
                setTimeout(() => {
                    newProblem();
                }, 1500);
            } else {
                const message = tryAgainMessages[Math.floor(Math.random() * tryAgainMessages.length)];
                feedbackElement.textContent = `${message} The answer is ${currentProblem.answer}`;
                feedbackElement.className = 'feedback incorrect';
                
                setTimeout(() => {
                    newProblem();
                }, 2500);
            }
            
            updateScoreBoard();
        }

        function updateScoreBoard() {
            document.getElementById('score').textContent = score;
            document.getElementById('correct').textContent = correct;
            document.getElementById('total').textContent = total;
        }

        function resetGame() {
            score = 0;
            correct = 0;
            total = 0;
            updateScoreBoard();
            document.getElementById('problem').textContent = "Click 'New Problem' to start!";
            document.getElementById('answer').value = '';
            document.getElementById('feedback').textContent = '';
            document.getElementById('feedback').className = 'feedback';
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                checkAnswer();
            }
        }

        // Initialize the game
        window.onload = function() {
            newProblem();
        };
    </script>
</body>
</html>
