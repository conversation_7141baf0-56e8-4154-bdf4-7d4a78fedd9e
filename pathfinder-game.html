<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kids Path Finder Game</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        h1 {
            color: #333;
            font-size: 2.5em;
            margin: 0;
            text-align: center;
        }

        .game-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 20px 0;
        }
        
        .game-info {
            text-align: center;
            margin-bottom: 15px;
            width: 100%;
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s ease;
            margin-top: 20px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        @media (max-width: 500px) {
            #gameCanvas {
                width: 100%;
                height: auto;
            }
            
            h1 {
                font-size: 2em;
            }
        }
        
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .instructions h2 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .instructions ul {
            margin-left: 20px;
            color: #666;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .level-selector {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .level-btn {
            padding: 8px 16px;
            background: #f8f9fa;
            border: 2px solid #667eea;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .level-btn:hover, .level-btn.active {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Kids Path Finder Game</h1>
        </div>
        
        <div class="instructions">
            <h2>How to Play:</h2>
            <ul>
                <li>Help Mihika find the path from the green start to the red finish</li>
                <li>Use arrow keys (↑ ↓ ← →) or swipe on mobile to move</li>
                <li>Avoid the purple walls</li>
                <li>Complete each level to unlock the next one</li>
            </ul>
        </div>
        
        <div class="game-container">
            <div class="game-info">
                <p>Current Level: <span id="gameLevel">1</span></p>
                <div class="level-selector">
                    <button class="level-btn active" data-level="1">Level 1</button>
                    <button class="level-btn" data-level="2">Level 2</button>
                    <button class="level-btn" data-level="3">Level 3</button>
                </div>
                <button id="startGameBtn" class="btn" style="margin-bottom: 15px;">Start Game</button>
            </div>
            <canvas id="gameCanvas" width="400" height="400" style="border:2px solid #667eea; border-radius: 8px;"></canvas>
        </div>
        
        <a href="index.html" class="btn" style="display: block; text-align: center; text-decoration: none;">Back to Main Menu</a>
    </div>

    <script>
        // Path Finder Game Logic
        document.getElementById('startGameBtn').addEventListener('click', startPathFinderGame);
        
        // Level selection
        const levelButtons = document.querySelectorAll('.level-btn');
        levelButtons.forEach(button => {
            button.addEventListener('click', function() {
                levelButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                document.getElementById('gameLevel').textContent = this.dataset.level;
            });
        });

        function startPathFinderGame() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            const cellSize = 40;
            const gridSize = 10;
            let level = parseInt(document.getElementById('gameLevel').textContent);
            let playerX = 0;
            let playerY = 0;
            let goalX = gridSize - 1;
            let goalY = gridSize - 1;
            
            // Maze definitions for different levels
            const mazes = {
                1: [
                    [0, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 1, 0, 1, 1, 1, 1, 1, 1, 0],
                    [0, 1, 0, 0, 0, 0, 0, 0, 1, 0],
                    [0, 1, 1, 1, 1, 1, 1, 0, 1, 0],
                    [0, 0, 0, 0, 0, 0, 1, 0, 1, 0],
                    [0, 1, 1, 1, 1, 0, 1, 0, 1, 0],
                    [0, 1, 0, 0, 1, 0, 1, 0, 1, 0],
                    [0, 1, 0, 1, 1, 0, 1, 0, 1, 0],
                    [0, 1, 0, 0, 0, 0, 0, 0, 1, 0],
                    [0, 0, 0, 1, 1, 1, 1, 0, 0, 0]
                ],
                2: [
                    [0, 0, 0, 1, 0, 0, 0, 1, 0, 0],
                    [1, 1, 0, 1, 0, 1, 0, 1, 0, 1],
                    [0, 0, 0, 1, 0, 1, 0, 0, 0, 0],
                    [0, 1, 1, 1, 0, 1, 1, 1, 1, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 1, 0],
                    [1, 1, 1, 1, 1, 1, 1, 0, 1, 0],
                    [0, 0, 0, 0, 0, 0, 1, 0, 1, 0],
                    [0, 1, 1, 1, 1, 0, 1, 0, 1, 0],
                    [0, 0, 0, 0, 1, 0, 0, 0, 1, 0],
                    [1, 1, 1, 0, 0, 0, 1, 0, 0, 0]
                ],
                3: [
                    [0, 0, 1, 0, 0, 0, 1, 0, 1, 0],
                    [1, 0, 1, 0, 1, 0, 1, 0, 1, 0],
                    [1, 0, 1, 0, 1, 0, 1, 0, 1, 0],
                    [1, 0, 1, 0, 1, 0, 1, 0, 1, 0],
                    [1, 0, 0, 0, 1, 0, 0, 0, 0, 0],
                    [1, 1, 1, 1, 1, 1, 1, 1, 1, 0],
                    [1, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [1, 0, 1, 1, 1, 1, 1, 1, 1, 1],
                    [1, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [1, 1, 1, 1, 1, 1, 1, 1, 1, 0]
                ]
            };
            
            let maze = mazes[level];
            
            // Draw the maze
            function drawMaze() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // Draw grid
                for (let y = 0; y < gridSize; y++) {
                    for (let x = 0; x < gridSize; x++) {
                        if (maze[y][x] === 1) {
                            // Wall
                            ctx.fillStyle = '#764ba2';
                            ctx.fillRect(x * cellSize, y * cellSize, cellSize, cellSize);
                        } else {
                            // Path
                            ctx.fillStyle = '#f8f9fa';
                            ctx.fillRect(x * cellSize, y * cellSize, cellSize, cellSize);
                            ctx.strokeStyle = '#ddd';
                            ctx.strokeRect(x * cellSize, y * cellSize, cellSize, cellSize);
                        }
                    }
                }
                
                // Draw start point
                ctx.fillStyle = 'green';
                ctx.beginPath();
                ctx.arc(0 * cellSize + cellSize/2, 0 * cellSize + cellSize/2, cellSize/3, 0, Math.PI * 2);
                ctx.fill();
                
                // Draw goal
                ctx.fillStyle = 'red';
                ctx.beginPath();
                ctx.arc(goalX * cellSize + cellSize/2, goalY * cellSize + cellSize/2, cellSize/3, 0, Math.PI * 2);
                ctx.fill();
                
                // Draw player
                ctx.fillStyle = 'blue';
                ctx.beginPath();
                ctx.arc(playerX * cellSize + cellSize/2, playerY * cellSize + cellSize/2, cellSize/4, 0, Math.PI * 2);
                ctx.fill();
            }
            
            // Handle keyboard input
            function handleKeyDown(e) {
                let newX = playerX;
                let newY = playerY;
                
                switch(e.key) {
                    case 'ArrowUp':
                        newY--;
                        break;
                    case 'ArrowDown':
                        newY++;
                        break;
                    case 'ArrowLeft':
                        newX--;
                        break;
                    case 'ArrowRight':
                        newX++;
                        break;
                }
                
                // Check if the move is valid
                if (newX >= 0 && newX < gridSize && newY >= 0 && newY < gridSize && maze[newY][newX] !== 1) {
                    playerX = newX;
                    playerY = newY;
                    drawMaze();
                    
                    // Check if player reached the goal
                    if (playerX === goalX && playerY === goalY) {
                        alert(`Congratulations! You completed level ${level}!`);
                        
                        // Unlock next level
                        if (level < 3) {
                            const nextLevelBtn = document.querySelector(`.level-btn[data-level="${level + 1}"]`);
                            nextLevelBtn.classList.remove('disabled');
                            nextLevelBtn.click();
                        }
                        
                        // Reset player position
                        playerX = 0;
                        playerY = 0;
                        drawMaze();
                    }
                }
            }
            
            // Add touch controls for mobile
            let touchStartX, touchStartY;
            
            canvas.addEventListener('touchstart', function(e) {
                touchStartX = e.touches[0].clientX;
                touchStartY = e.touches[0].clientY;
                e.preventDefault();
            }, false);
            
            canvas.addEventListener('touchend', function(e) {
                if (!touchStartX || !touchStartY) return;
                
                let touchEndX = e.changedTouches[0].clientX;
                let touchEndY = e.changedTouches[0].clientY;
                
                let diffX = touchEndX - touchStartX;
                let diffY = touchEndY - touchStartY;
                
                // Determine swipe direction
                if (Math.abs(diffX) > Math.abs(diffY)) {
                    // Horizontal swipe
                    if (diffX > 0) {
                        handleKeyDown({key: 'ArrowRight'});
                    } else {
                        handleKeyDown({key: 'ArrowLeft'});
                    }
                } else {
                    // Vertical swipe
                    if (diffY > 0) {
                        handleKeyDown({key: 'ArrowDown'});
                    } else {
                        handleKeyDown({key: 'ArrowUp'});
                    }
                }
                
                touchStartX = null;
                touchStartY = null;
                e.preventDefault();
            }, false);
            
            // Start the game
            playerX = 0;
            playerY = 0;
            drawMaze();
            window.addEventListener('keydown', handleKeyDown);
            
            // Update button text
            document.getElementById('startGameBtn').textContent = "Restart Game";
        }
    </script>
</body>
</html>
