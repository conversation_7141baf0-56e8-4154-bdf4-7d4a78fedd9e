<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚂 Train Pathfinder Game 🚂</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #333;
        }

        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 600px;
            width: 95%;
        }

        h1 {
            color: #ff6b6b;
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .instructions {
            background: #e7f3ff;
            border: 2px solid #0066cc;
            border-radius: 15px;
            padding: 15px;
            margin: 20px 0;
            font-size: 1.1em;
            color: #0066cc;
        }

        .game-board {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(8, 1fr);
            gap: 2px;
            background: #333;
            border-radius: 10px;
            padding: 10px;
            margin: 20px auto;
            max-width: 400px;
            aspect-ratio: 1;
        }

        .cell {
            background: #90EE90;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            transition: all 0.3s ease;
        }

        .cell.wall {
            background: #8B4513;
        }

        .cell.train {
            background: #FFD700;
            animation: pulse 1s infinite;
        }

        .cell.station {
            background: #FF69B4;
            animation: glow 2s infinite;
        }

        .cell.path {
            background: #87CEEB;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 5px #FF69B4; }
            50% { box-shadow: 0 0 20px #FF69B4; }
        }

        .controls {
            margin: 20px 0;
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .control-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            transition: all 0.3s ease;
            min-width: 80px;
        }

        .control-btn:hover {
            background: #45a049;
            transform: scale(1.05);
        }

        .control-btn:active {
            transform: scale(0.95);
        }

        .status {
            font-size: 1.3em;
            font-weight: bold;
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .status.playing {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #bee5eb;
        }

        .status.won {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
            animation: celebration 0.6s ease-in-out;
        }

        .status.lost {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
            animation: shake 0.6s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes celebration {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            80% { transform: translateY(-5px); }
        }

        .level-selector {
            margin: 20px 0;
        }

        .level-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .level-btn:hover {
            background: #0056b3;
        }

        .level-btn.active {
            background: #dc3545;
            transform: scale(1.1);
        }

        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            margin-bottom: 20px;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #5a6268;
            transform: scale(1.05);
        }

        .moves-counter {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 10px;
            margin: 10px 0;
            font-size: 1.2em;
            font-weight: bold;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <button class="back-btn" onclick="backToMainGame()">← Back to Fun Games</button>
        <h1>🚂 Train Pathfinder 🚂</h1>
        
        <div class="instructions">
            🎯 Help the train reach the station! Use arrow keys or buttons to move.
            <br>🟡 = Train | 🟣 = Station | 🟢 = Safe Path | 🟤 = Wall
        </div>

        <div class="level-selector">
            <select id="levelSelect" onchange="setLevel(this.value)" style="padding: 10px; font-size: 1.1em; border-radius: 10px; border: 2px solid #007bff; background: white;">
                <option value="1">Level 1</option>
            </select>
        </div>

        <div class="moves-counter" id="movesCounter">Moves: 0 / 0</div>
        <div class="moves-counter" id="movesLimit" style="background: #f8d7da; border-color: #f5c6cb; color: #721c24;">Move Limit: 0</div>

        <div class="game-board" id="gameBoard"></div>

        <div class="controls">
            <button class="control-btn" onclick="movePlayer('up')">⬆️ Up</button>
            <button class="control-btn" onclick="movePlayer('down')">⬇️ Down</button>
            <button class="control-btn" onclick="movePlayer('left')">⬅️ Left</button>
            <button class="control-btn" onclick="movePlayer('right')">➡️ Right</button>
        </div>

        <div class="status playing" id="status">Use arrow keys or buttons to move the train! 🚂</div>

        <button class="control-btn" onclick="resetLevel()" style="background: #ffc107; color: #333;">🔄 Reset Level</button>
    </div>

    <script>
        let currentLevel = 1;
        let playerPos = { x: 0, y: 0 };
        let stationPos = { x: 7, y: 7 };
        let moves = 0;
        let maxMoves = 0;
        let gameWon = false;
        let gameLost = false;

        const levels = {
            1: { walls: [{x: 7, y: 1}, {x: 7, y: 2}, {x: 6, y: 7}, {x: 5, y: 7}], start: {x: 0, y: 0}, station: {x: 7, y: 7}, maxMoves: 18 },
            2: { walls: [{x: 1, y: 0}, {x: 2, y: 0}, {x: 7, y: 3}, {x: 7, y: 4}, {x: 4, y: 7}, {x: 5, y: 7}], start: {x: 0, y: 0}, station: {x: 7, y: 7}, maxMoves: 20 },
            3: { walls: [{x: 0, y: 3}, {x: 0, y: 4}, {x: 3, y: 0}, {x: 4, y: 0}, {x: 7, y: 2}, {x: 7, y: 3}, {x: 2, y: 7}, {x: 3, y: 7}], start: {x: 0, y: 0}, station: {x: 7, y: 7}, maxMoves: 22 },
            4: { walls: [{x: 0, y: 2}, {x: 0, y: 3}, {x: 0, y: 4}, {x: 2, y: 0}, {x: 3, y: 0}, {x: 4, y: 0}, {x: 7, y: 3}, {x: 7, y: 4}, {x: 7, y: 5}, {x: 3, y: 7}], start: {x: 0, y: 0}, station: {x: 7, y: 7}, maxMoves: 24 },
            5: { walls: [{x: 0, y: 2}, {x: 0, y: 3}, {x: 2, y: 0}, {x: 3, y: 0}, {x: 7, y: 4}, {x: 7, y: 5}, {x: 4, y: 7}, {x: 5, y: 7}, {x: 2, y: 3}, {x: 3, y: 3}, {x: 4, y: 3}, {x: 5, y: 4}], start: {x: 0, y: 0}, station: {x: 7, y: 7}, maxMoves: 26 },
            6: { walls: [{x: 0, y: 2}, {x: 0, y: 3}, {x: 2, y: 0}, {x: 3, y: 0}, {x: 4, y: 0}, {x: 7, y: 3}, {x: 7, y: 4}, {x: 7, y: 5}, {x: 3, y: 7}, {x: 4, y: 7}, {x: 5, y: 7}, {x: 2, y: 3}, {x: 3, y: 3}, {x: 4, y: 3}], start: {x: 0, y: 0}, station: {x: 7, y: 7}, maxMoves: 28 },
            7: { walls: [{x: 0, y: 2}, {x: 0, y: 4}, {x: 2, y: 0}, {x: 4, y: 0}, {x: 7, y: 2}, {x: 7, y: 4}, {x: 2, y: 7}, {x: 4, y: 7}, {x: 2, y: 2}, {x: 3, y: 2}, {x: 4, y: 2}, {x: 2, y: 5}, {x: 3, y: 5}, {x: 4, y: 5}, {x: 5, y: 3}, {x: 5, y: 4}, {x: 6, y: 3}], start: {x: 0, y: 0}, station: {x: 7, y: 7}, maxMoves: 30 },
            8: { walls: [{x: 0, y: 2}, {x: 0, y: 4}, {x: 2, y: 0}, {x: 4, y: 0}, {x: 7, y: 2}, {x: 7, y: 4}, {x: 2, y: 7}, {x: 4, y: 7}, {x: 1, y: 2}, {x: 2, y: 2}, {x: 3, y: 2}, {x: 1, y: 5}, {x: 2, y: 5}, {x: 3, y: 5}, {x: 5, y: 1}, {x: 5, y: 2}, {x: 5, y: 4}, {x: 5, y: 5}, {x: 6, y: 3}, {x: 6, y: 4}], start: {x: 0, y: 0}, station: {x: 7, y: 7}, maxMoves: 32 },
            9: { walls: [{x: 0, y: 3}, {x: 0, y: 5}, {x: 3, y: 0}, {x: 5, y: 0}, {x: 7, y: 2}, {x: 7, y: 4}, {x: 2, y: 7}, {x: 4, y: 7}, {x: 2, y: 2}, {x: 3, y: 2}, {x: 2, y: 4}, {x: 3, y: 4}, {x: 5, y: 2}, {x: 5, y: 3}, {x: 5, y: 5}, {x: 1, y: 6}, {x: 3, y: 6}, {x: 6, y: 1}, {x: 6, y: 3}, {x: 6, y: 5}, {x: 1, y: 2}, {x: 4, y: 1}, {x: 4, y: 4}], start: {x: 0, y: 0}, station: {x: 7, y: 7}, maxMoves: 34 },
            10: { walls: [{x: 0, y: 3}, {x: 0, y: 5}, {x: 3, y: 0}, {x: 5, y: 0}, {x: 7, y: 2}, {x: 7, y: 4}, {x: 2, y: 7}, {x: 4, y: 7}, {x: 1, y: 1}, {x: 2, y: 1}, {x: 4, y: 1}, {x: 1, y: 3}, {x: 2, y: 3}, {x: 4, y: 3}, {x: 1, y: 5}, {x: 2, y: 5}, {x: 4, y: 5}, {x: 5, y: 2}, {x: 5, y: 4}, {x: 5, y: 6}, {x: 6, y: 1}, {x: 6, y: 3}, {x: 6, y: 5}, {x: 3, y: 2}, {x: 3, y: 4}, {x: 3, y: 6}], start: {x: 0, y: 0}, station: {x: 7, y: 7}, maxMoves: 36 }
        };

        function initGame() {
            const level = levels[currentLevel];
            playerPos = { ...level.start };
            stationPos = { ...level.station };
            maxMoves = level.maxMoves;
            moves = 0;
            gameWon = false;
            gameLost = false;
            updateMovesCounter();
            updateMovesLimit();
            updateStatus("Use arrow keys or buttons to move the train! 🚂", "playing");
            createBoard();
        }

        function populateLevelSelector() {
            const select = document.getElementById('levelSelect');
            select.innerHTML = '';
            for (let i = 1; i <= 10; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `Level ${i}`;
                if (i === currentLevel) option.selected = true;
                select.appendChild(option);
            }
        }

        function createBoard() {
            const board = document.getElementById('gameBoard');
            board.innerHTML = '';
            
            const level = levels[currentLevel];
            
            for (let y = 0; y < 8; y++) {
                for (let x = 0; x < 8; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.id = `cell-${x}-${y}`;
                    
                    // Check if this position is a wall
                    const isWall = level.walls.some(wall => wall.x === x && wall.y === y);
                    
                    if (x === playerPos.x && y === playerPos.y) {
                        cell.classList.add('train');
                        cell.textContent = '🚂';
                    } else if (x === stationPos.x && y === stationPos.y) {
                        cell.classList.add('station');
                        cell.textContent = '🏁';
                    } else if (isWall) {
                        cell.classList.add('wall');
                        cell.textContent = '🧱';
                    }
                    
                    board.appendChild(cell);
                }
            }
        }

        function movePlayer(direction) {
            if (gameWon || gameLost) return;

            let newX = playerPos.x;
            let newY = playerPos.y;

            switch(direction) {
                case 'up': newY--; break;
                case 'down': newY++; break;
                case 'left': newX--; break;
                case 'right': newX++; break;
            }

            // Check boundaries
            if (newX < 0 || newX > 7 || newY < 0 || newY > 7) {
                return;
            }

            // Check walls
            const level = levels[currentLevel];
            const isWall = level.walls.some(wall => wall.x === newX && wall.y === newY);
            if (isWall) {
                return;
            }

            // Move player
            playerPos.x = newX;
            playerPos.y = newY;
            moves++;
            updateMovesCounter();

            // Check if exceeded move limit
            if (moves > maxMoves) {
                gameLost = true;
                updateStatus(`😞 Too many moves! You used ${moves} but limit was ${maxMoves}. Try again! 🔄`, "lost");
                return;
            }

            // Mark path
            const oldCell = document.getElementById(`cell-${playerPos.x}-${playerPos.y}`);
            if (oldCell && !oldCell.classList.contains('station')) {
                oldCell.classList.add('path');
            }

            createBoard();

            // Check win condition
            if (playerPos.x === stationPos.x && playerPos.y === stationPos.y) {
                gameWon = true;
                if (moves <= maxMoves) {
                    updateStatus(`🎉 Perfect! Train reached station in ${moves}/${maxMoves} moves! 🎉`, "won");

                    // Auto-advance to next level after 2 seconds
                    setTimeout(() => {
                        if (currentLevel < 10) {
                            currentLevel++;
                            document.getElementById('levelSelect').value = currentLevel;
                            initGame();
                            updateStatus(`🚂 Welcome to Level ${currentLevel}! Find the best path! 🎯`, "playing");
                        } else {
                            updateStatus(`🏆 Congratulations! You completed all 10 levels! You're a Pathfinder Master! 🏆`, "won");
                        }
                    }, 2000);
                }
            }
        }

        function updateMovesCounter() {
            document.getElementById('movesCounter').textContent = `Moves: ${moves} / ${maxMoves}`;
        }

        function updateMovesLimit() {
            document.getElementById('movesLimit').textContent = `Move Limit: ${maxMoves}`;
        }

        function updateStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function setLevel(level) {
            currentLevel = parseInt(level);
            initGame();
        }

        function resetLevel() {
            initGame();
        }

        function backToMainGame() {
            window.location.href = 'fun-games.html';
        }

        // Keyboard controls
        document.addEventListener('keydown', function(event) {
            switch(event.key) {
                case 'ArrowUp': movePlayer('up'); break;
                case 'ArrowDown': movePlayer('down'); break;
                case 'ArrowLeft': movePlayer('left'); break;
                case 'ArrowRight': movePlayer('right'); break;
            }
        });

        // Initialize game on load
        window.onload = function() {
            populateLevelSelector();
            initGame();
        };
    </script>
</body>
</html>
